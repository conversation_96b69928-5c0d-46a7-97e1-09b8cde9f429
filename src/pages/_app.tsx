import "tailwindcss/tailwind.css";
import "styles/index.css";
import type { AppProps } from "next/app";
import LoadingBar from "react-top-loading-bar";
import LoaderProvider, { useLoaderContext } from "context/loaderContext";
import ClientOnly from "common/components/ClientOnly";

export default function App({ Component, pageProps }: AppProps) {
  return (
    <>
      <LoaderProvider>
          <Component {...pageProps} />
      </LoaderProvider>
    </>
  );
}
