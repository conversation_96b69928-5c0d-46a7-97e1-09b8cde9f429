import { useEffect, useMemo, useState } from "react";
import { CustomSearch } from "common/ui/inputs/search";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "common/ui/tabs";
import Block from "common/components/composer/sidebar/block";
import blocks from "data/composer/blocks";
import searchedArray from "helpers/search";
import useLeadsAndReportStore from "store/leadsAndReports/leadsAndReports.store";
import { ITrigger } from "store/trigger/trigger.types";
import { toast } from "react-hot-toast";
import useLiveChatStore from "store/livechat/livechat.store";
import { IDialog } from "store/dialog/dialog.types";
import useGenesysStore from "store/genesys/genesys.store";
import useSendgridStore from "store/sendgrid/sendgrid.store";
import useSmtpStore from "store/smtp/smtp.store";
import useTicketingStore from "store/ticketing/ticketing.store";
import useLLMStore from "store/llmIntegration/llm.store";
interface Props {
  dialogTriggers: ITrigger[];
  dialog: IDialog;
}

const SideBar: React.FC<Props> = ({ dialogTriggers, dialog }) => {
  const [keySearch, setKeySearch] = useState("");
  const { leadsActive, reportsActive } = useLeadsAndReportStore();
  const { livechat, get_one_livechat } = useLiveChatStore();
  const { genesys, get_one_genesys } = useGenesysStore();
  const { sendgrid, get_one_sendgrid } = useSendgridStore();
  const { llm } = useLLMStore();
  const { ticketing} = useTicketingStore();
  const { Smtp, get_one_Smtp } = useSmtpStore();
  const availabalityDepObj = useMemo(() => {
    return {
      lead_dep: leadsActive,
      report_dep: reportsActive,
      lc_dep: true,
      // lc_dep: livechat.livechat_integration_id || genesys.genesys_integration_id,
      sg_dep: sendgrid.sendGrid_id || Smtp.smtp_id,
      ticketing_dep: ticketing?.status === 'active',
      // llm_dep: true
      llm_dep: llm?.status_active
    };
  }, [livechat, reportsActive, leadsActive,genesys,sendgrid,Smtp, ticketing,llm]);

  const simpleBlocks = useMemo(() => {
    return blocks.filter((block) => block.isSimple);
  }, [blocks]);

  const advancedBlocks = useMemo(() => {
    const proceedBlocks = [];
    blocks.map((block) => {
      if (!block.avaliabitlyDependency) {
        proceedBlocks.push(block);
      } else if (availabalityDepObj[block.avaliabitlyDependency]) {
        proceedBlocks.push(block);
      }
    });
    return proceedBlocks;
  }, [blocks]);

  const groupedBlocks = useMemo(() => {
    return advancedBlocks.reduce((groups, obj) => {
      const groupKey = obj.group;
      if (!groups[groupKey]) {
        groups[groupKey] = [];
      }
      groups[groupKey].push(obj);
      return groups;
    }, {});
  }, [advancedBlocks]);

  const filteredAdvancedBlocks = useMemo(() => {
    return searchedArray(keySearch, advancedBlocks, ["block_type"]);
  }, [keySearch, advancedBlocks]);
  useEffect(() => {
    get_one_livechat(dialog.bot_id);
    get_one_Smtp(dialog.bot_id);
    get_one_sendgrid(dialog.bot_id);
    get_one_genesys(dialog.bot_id);
  }, [dialog]);
  return (
    <div className="relative flex flex-col justify-between items-center w-full h-full max-h-full  text-gray-400 border-r-2 border-gray-800 bg-black shadow-xl ">
      <Tabs defaultValue="simple" className="w-full">
        <TabsList className="flex justify-center border-b-2 p-1 border-gray-800  w-full rounded-none">
          <TabsTrigger value="simple">Simple</TabsTrigger>
          <TabsTrigger value="advanced">Advanced</TabsTrigger>
        </TabsList>
        <TabsContent
          value="simple"
          className="border-0 p-2 max-h-full overflow-y-auto"
        >
          {dialogTriggers?.length > 0 ? (
            <div className="grid grid-cols-1 gap-4 px-2">
              {simpleBlocks.map((block) => (
                <Block key={"simple-block-" + block.block_type} block={block} />
              ))}
            </div>
          ) : (
            <div className="h-full py-64 w-full text-center">
              🔒 Please add a trigger first
            </div>
          )}
        </TabsContent>
        <TabsContent
          value="advanced"
          className="border-0 p-2 h-[calc(100vh-100px)] overflow-y-auto"
        >
          {dialogTriggers?.length > 0 ? (
            <>
              <CustomSearch
                placeholder="Search for a feature"
                onChange={(value) => {
                  setKeySearch(value);
                }}
              />
              <div className="grid grid-cols-1 gap-4 px-2 mt-4">
                {keySearch ? (
                  filteredAdvancedBlocks.map((block) => (
                    <Block
                      key={"advanced-block-" + block.block_type}
                      block={block}
                    />
                  ))
                ) : (
                  <>
                    {Object.keys(groupedBlocks).map((group) => (
                      <div key={"group-" + group}>
                        <div className="text-gray-500 text-sm font-semibold mt-3 mb-2">
                          {group}
                        </div>
                        {groupedBlocks[group].map((block) => (
                          <Block
                            key={"advanced-block-" + block.block_type}
                            block={block}
                          />
                        ))}
                      </div>
                    ))}
                  </>
                )}
              </div>
            </>
          ) : (
            <div className="h-full py-64 w-full text-center">
              🔒 Please add a trigger first
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default SideBar;
